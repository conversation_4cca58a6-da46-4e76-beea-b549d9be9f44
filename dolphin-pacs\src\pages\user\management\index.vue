<script lang="ts" setup>
import { ref, reactive, onMounted, useTemplateRef } from "vue";
import { ElMessage } from "element-plus";
import type { VxeGridInstance, VxeGridProps } from "vxe-table";
import type { UserManagementInfo, UserSearchParams } from "@/common/apis/users/type";
import type { RoleInfo } from "@/common/apis/roles/type";
import { Search, Refresh } from "@element-plus/icons-vue";
import { getUserListApi } from "@/common/apis/users";
import { getRoleListApi } from "@/common/apis/roles";

defineOptions({
  // 命名当前组件
  name: "UserManagement",
});

// 表格实例引用
const xGridDom = useTemplateRef<VxeGridInstance>("xGridDom");

// 搜索表单数据
const searchForm = reactive<UserSearchParams>({
  username: "",
  nickname: "",
  phone: "",
  email: "",
});

// 表格数据
const tableData = ref<UserManagementInfo[]>([]);

// 角色分配弹窗相关状态
const assignRoleDialogVisible = ref(false);
const currentUser = ref<UserManagementInfo | null>(null);
const roleList = ref<RoleInfo[]>([]);
const selectedRole = ref<string>("");
const roleLoading = ref(false);

// 表格配置
const xGridOpt = reactive<VxeGridProps>({
  border: true,
  stripe: true,
  resizable: true,
  showHeaderOverflow: true,
  showOverflow: true,
  keepSource: true,
  id: "UserManagement",
  height: 600,
  loading: false,
  data: tableData as any,
  /** 工具栏配置 */
  toolbarConfig: {
    refresh: true,
    custom: true,
    slots: {
      buttons: "toolbar-btns",
    },
  },
  /** 自定义列配置项 */
  customConfig: {
    /** 是否允许列选中  */
    checkMethod: ({ column }: { column: any }) =>
      !["username"].includes(column.field),
  },
  /** 列配置 */
  columns: [
    {
      type: "checkbox",
      width: 50,
    },
    {
      type: "seq",
      width: 70,
      title: "序号",
    },
    {
      field: "id",
      title: "用户编号",
      width: 100,
    },
    {
      field: "username",
      title: "用户名称",
      minWidth: 120,
    },
    {
      field: "nickname",
      title: "用户昵称",
      minWidth: 120,
    },
    {
      field: "gender",
      title: "性别",
      width: 80,
      slots: { default: "gender-column" },
    },
    {
      field: "phone",
      title: "手机号码",
      width: 140,
    },
    {
      field: "email",
      title: "邮箱",
      minWidth: 180,
      showOverflow: "tooltip",
    },
    {
      title: "操作",
      width: 120,
      fixed: "right",
      slots: { default: "action-column" },
    },
  ],
});

// 用户数据加载函数
const loadUserData = async () => {
  try {
    xGridOpt.loading = true;

    const params: UserSearchParams = {
      username: searchForm.username || undefined,
      nickname: searchForm.nickname || undefined,
      phone: searchForm.phone || undefined,
      email: searchForm.email || undefined,
    };

    const response = await getUserListApi(params);

    // 打印响应数据结构用于调试
    console.log("用户API响应:", response);

    // 检查响应数据结构并确保返回数组
    let userData: UserManagementInfo[] = [];

    if (response && response.data) {
      if (Array.isArray(response.data)) {
        userData = response.data;
        console.log("用户数据:", userData);
        
      } else {
        console.warn("API返回的data不是数组格式:", response.data);
        userData = [];
      }
    }

    // 过滤数据（如果有搜索条件）
    if (params.username || params.nickname || params.phone || params.email) {
      userData = userData.filter((user) => {
        return (
          (!params.username || user.username.includes(params.username)) &&
          (!params.nickname || user.nickname.includes(params.nickname)) &&
          (!params.phone || user.phone.includes(params.phone)) &&
          (!params.email || user.email.includes(params.email))
        );
      });
    }

    // 直接设置表格数据
    tableData.value = userData;
    xGridOpt.loading = false;

  } catch (error) {
    console.error("获取用户数据失败:", error);
    ElMessage.error("获取用户数据失败，请稍后重试");
    tableData.value = [];
    xGridOpt.loading = false;
  }
};

// 查询数据
const handleQuery = () => {
  loadUserData();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    username: "",
    nickname: "",
    phone: "",
    email: "",
  });
  loadUserData();
};

// 获取性别标签类型
const getGenderTagType = (gender: string) => {
  switch (gender) {
    case "男":
      return "primary";
    case "女":
      return "danger";
    default:
      return "info";
  }
};

// 新增用户
const handleAdd = () => {
  ElMessage.info("新增用户功能待实现");
};

// 批量删除
const handleBatchDelete = () => {
  const selectRecords = xGridDom.value?.getCheckboxRecords();
  if (!selectRecords || selectRecords.length === 0) {
    ElMessage.warning("请选择要删除的用户");
    return;
  }
  ElMessage.info("批量删除功能待实现");
};

// 获取角色列表
const loadRoleList = async (userId: number) => {
  try {
    roleLoading.value = true;

    const response = await getRoleListApi({ userId });

    console.log("角色API响应:", response);

    // 检查响应数据结构并确保返回数组
    if (response && response.data && Array.isArray(response.data)) {
      roleList.value = response.data;
    } else {
      console.warn("角色API返回的data不是数组格式:", response);
      roleList.value = [];
    }
  } catch (error) {
    console.error("获取角色列表失败:", error);
    ElMessage.error("获取角色列表失败，请稍后重试");
    roleList.value = [];
  } finally {
    roleLoading.value = false;
  }
};

// 分配角色
const handleAssignRole = async (row: UserManagementInfo) => {
  currentUser.value = row;
  selectedRole.value = "";
  assignRoleDialogVisible.value = true;

  // 加载角色列表
  await loadRoleList(row.id);
};

// 关闭角色分配弹窗
const handleCloseAssignDialog = () => {
  assignRoleDialogVisible.value = false;
  currentUser.value = null;
  selectedRole.value = "";
  roleList.value = [];
};

// 确认分配角色
const handleConfirmAssignRole = () => {
  if (!selectedRole.value) {
    ElMessage.warning("请选择要分配的角色");
    return;
  }

  if (!currentUser.value) {
    ElMessage.error("用户信息丢失，请重新操作");
    return;
  }

  // TODO: 这里应该调用角色分配的API接口
  ElMessage.success(`已为用户 ${currentUser.value.username} 分配角色: ${selectedRole.value}`);
  handleCloseAssignDialog();
};

onMounted(() => {
  // 组件挂载后加载用户数据
  loadUserData();
});
</script>

<template>
  <div class="user-management">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card class="search-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="用户名称">
            <el-input
              v-model="searchForm.username"
              placeholder="请输入用户名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="用户昵称">
            <el-input
              v-model="searchForm.nickname"
              placeholder="请输入用户昵称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input
              v-model="searchForm.phone"
              placeholder="请输入手机号码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input
              v-model="searchForm.email"
              placeholder="请输入邮箱"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleQuery">
              搜索
            </el-button>
            <el-button :icon="Refresh" @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-card class="table-card">
        <vxe-grid ref="xGridDom" v-bind="xGridOpt">
          <!-- 工具栏按钮 -->
          <template #toolbar-btns>
            <vxe-button status="primary" icon="vxe-icon-add" @click="handleAdd">
              新增用户
            </vxe-button>
            <vxe-button status="danger" icon="vxe-icon-delete" @click="handleBatchDelete">
              批量删除
            </vxe-button>
          </template>

          <!-- 性别列 -->
          <template #gender-column="{ row, column }">
            <el-tag
              :type="getGenderTagType(row[column.field])"
              effect="light"
              size="small"
            >
              {{ row[column.field] }}
            </el-tag>
          </template>

          <!-- 操作列 -->
          <template #action-column="{ row }">
            <el-button
              link
              type="primary"
              size="small"
              @click="handleAssignRole(row)"
            >
              分配角色
            </el-button>
          </template>
        </vxe-grid>
      </el-card>
    </div>

    <!-- 角色分配弹窗 -->
    <el-dialog
      v-model="assignRoleDialogVisible"
      title="分配角色"
      width="500px"
      :before-close="handleCloseAssignDialog"
    >
      <div v-if="currentUser" class="assign-role-content">
        <div class="user-info">
          <p><strong>用户编号：</strong>{{ currentUser.id }}</p>
          <p><strong>用户名称：</strong>{{ currentUser.username }}</p>
          <p><strong>用户昵称：</strong>{{ currentUser.nickname }}</p>
        </div>

        <div class="role-select">
          <el-form-item label="选择角色：">
            <el-select
              v-model="selectedRole"
              placeholder="请选择角色"
              style="width: 100%"
              :loading="roleLoading"
              clearable
            >
              <el-option
                v-for="role in roleList"
                :key="role.id"
                :label="role.code"
                :value="role.code"
              >
                <span>{{ role.code }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ role.name }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseAssignDialog">取消</el-button>
          <el-button type="primary" @click="handleConfirmAssignRole">
            确认分配
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.user-management {
  padding: 20px;
  
  .search-section {
    margin-bottom: 20px;
    
    .search-card {
      :deep(.el-card__body) {
        padding: 20px;
      }
    }
  }
  
  .table-section {
    .table-card {
      :deep(.el-card__body) {
        padding: 0;
      }
    }
  }
}
</style>
