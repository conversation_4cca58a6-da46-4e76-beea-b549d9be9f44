import type * as Roles from "./type"
import { request } from "@/http/axios"

/**
 * 获取角色列表
 * @param params 搜索参数
 * @returns 角色列表数据
 */
export function getRoleListApi(params?: Roles.RoleSearchParams) {
  return request<Roles.RoleListApiResponse>({
    url: "auth/role/list",
    method: "get",
    params
  })
}

/**
 * 新增角色
 * @param data 角色数据
 * @returns 新增结果
 */
export function addRoleApi(data: Roles.AddRoleRequestData) {
  return request<Roles.AddRoleApiResponse>({
    url: "auth/role/add",
    method: "post",
    data
  })
}
